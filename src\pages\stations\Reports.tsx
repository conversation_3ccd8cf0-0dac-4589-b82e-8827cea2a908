import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Calendar,
  Download,
  Printer,
  RefreshCw,
  FileText,
  PieChart,
  Activity,
} from "lucide-react";
import { toast } from "sonner";

interface ReportData {
  totalSales: number;
  totalOrders: number;
  totalCustomers: number;
  averageOrderValue: number;
  topItems: Array<{ name: string; quantity: number; revenue: number }>;
  salesByHour: Array<{ hour: string; sales: number }>;
  salesTrend: "up" | "down" | "stable";
  trendPercentage: number;
}

const Reports: React.FC = () => {
  const [reportType, setReportType] = useState("daily");
  const [dateFrom, setDateFrom] = useState("2024-01-20");
  const [dateTo, setDateTo] = useState("2024-01-20");
  const [isGenerating, setIsGenerating] = useState(false);

  const mockReportData: ReportData = {
    totalSales: 2847.50,
    totalOrders: 156,
    totalCustomers: 89,
    averageOrderValue: 18.25,
    topItems: [
      { name: "Cappuccino", quantity: 45, revenue: 157.50 },
      { name: "Caesar Salad", quantity: 23, revenue: 298.77 },
      { name: "Grilled Chicken", quantity: 18, revenue: 333.00 },
      { name: "Chocolate Cake", quantity: 31, revenue: 216.69 },
      { name: "Latte", quantity: 38, revenue: 161.50 },
    ],
    salesByHour: [
      { hour: "09:00", sales: 125.50 },
      { hour: "10:00", sales: 189.25 },
      { hour: "11:00", sales: 234.75 },
      { hour: "12:00", sales: 456.80 },
      { hour: "13:00", sales: 523.40 },
      { hour: "14:00", sales: 398.60 },
      { hour: "15:00", sales: 287.30 },
      { hour: "16:00", sales: 312.45 },
      { hour: "17:00", sales: 319.45 },
    ],
    salesTrend: "up",
    trendPercentage: 12.5,
  };

  const reportTypes = [
    { value: "daily", label: "Daily Report" },
    { value: "weekly", label: "Weekly Report" },
    { value: "monthly", label: "Monthly Report" },
    { value: "custom", label: "Custom Range" },
  ];

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    
    setTimeout(() => {
      toast.success("Report generated successfully!");
      setIsGenerating(false);
    }, 2000);
  };

  const handleExportReport = (format: string) => {
    toast.success(`Report exported as ${format.toUpperCase()}`);
  };

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    trend, 
    trendValue, 
    color = "text-primary" 
  }: {
    title: string;
    value: string | number;
    icon: any;
    trend?: "up" | "down" | "stable";
    trendValue?: number;
    color?: string;
  }) => (
    <Card className="shadow-lg border-border/50 hover:shadow-xl transition-all duration-300">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground mb-1">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>{value}</p>
            {trend && trendValue && (
              <div className="flex items-center gap-1 mt-2">
                {trend === "up" ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : trend === "down" ? (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                ) : (
                  <Activity className="h-4 w-4 text-gray-600" />
                )}
                <span className={`text-sm ${
                  trend === "up" ? "text-green-600" : 
                  trend === "down" ? "text-red-600" : "text-gray-600"
                }`}>
                  {trendValue}%
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full bg-primary/10`}>
            <Icon className={`h-6 w-6 ${color}`} />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/stations/home">
            <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
              <ArrowLeft className="h-4 w-4" />
              Back to Menu
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Reports & Analytics
            </h1>
            <p className="text-muted-foreground">
              View sales reports and business analytics
            </p>
          </div>
        </div>

        <Card className="shadow-lg border-border/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              <BarChart3 className="h-5 w-5 text-primary" />
              Generate Report
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div>
                <Label htmlFor="reportType">Report Type</Label>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger className="border-border/50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {reportType === "custom" && (
                <>
                  <div>
                    <Label htmlFor="dateFrom">From Date</Label>
                    <Input
                      id="dateFrom"
                      type="date"
                      value={dateFrom}
                      onChange={(e) => setDateFrom(e.target.value)}
                      className="border-border/50"
                    />
                  </div>
                  <div>
                    <Label htmlFor="dateTo">To Date</Label>
                    <Input
                      id="dateTo"
                      type="date"
                      value={dateTo}
                      onChange={(e) => setDateTo(e.target.value)}
                      className="border-border/50"
                    />
                  </div>
                </>
              )}
              
              <div className="flex items-end gap-2">
                <Button
                  onClick={handleGenerateReport}
                  disabled={isGenerating}
                  className="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-600 text-white hover:scale-105 transition-all duration-200"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Generate
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => handleExportReport("pdf")}
                className="hover:scale-105 transition-all duration-200"
              >
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
              <Button
                variant="outline"
                onClick={() => handleExportReport("excel")}
                className="hover:scale-105 transition-all duration-200"
              >
                <FileText className="h-4 w-4 mr-2" />
                Export Excel
              </Button>
              <Button
                variant="outline"
                onClick={() => handleExportReport("print")}
                className="hover:scale-105 transition-all duration-200"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Sales"
            value={`$${mockReportData.totalSales.toFixed(2)}`}
            icon={DollarSign}
            trend={mockReportData.salesTrend}
            trendValue={mockReportData.trendPercentage}
            color="text-green-600"
          />
          <StatCard
            title="Total Orders"
            value={mockReportData.totalOrders}
            icon={ShoppingCart}
            trend="up"
            trendValue={8.2}
            color="text-blue-600"
          />
          <StatCard
            title="Total Customers"
            value={mockReportData.totalCustomers}
            icon={Users}
            trend="up"
            trendValue={5.7}
            color="text-purple-600"
          />
          <StatCard
            title="Avg Order Value"
            value={`$${mockReportData.averageOrderValue.toFixed(2)}`}
            icon={TrendingUp}
            trend="stable"
            trendValue={2.1}
            color="text-orange-600"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="shadow-lg border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                <PieChart className="h-5 w-5 text-primary" />
                Top Selling Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockReportData.topItems.map((item, index) => (
                  <div key={item.name} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-muted-foreground">{item.quantity} sold</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-primary">${item.revenue.toFixed(2)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                <BarChart3 className="h-5 w-5 text-primary" />
                Sales by Hour
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockReportData.salesByHour.map((hourData) => {
                  const maxSales = Math.max(...mockReportData.salesByHour.map(h => h.sales));
                  const percentage = (hourData.sales / maxSales) * 100;
                  
                  return (
                    <div key={hourData.hour} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{hourData.hour}</span>
                        <span className="text-primary font-bold">${hourData.sales.toFixed(2)}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-primary to-primary/70 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="shadow-lg border-border/50">
          <CardHeader>
            <CardTitle className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Report Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h4 className="font-semibold text-foreground">Peak Hours</h4>
                <p className="text-muted-foreground">12:00 PM - 2:00 PM</p>
                <p className="text-sm text-muted-foreground">Highest sales volume during lunch hours</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-foreground">Best Performing Category</h4>
                <p className="text-muted-foreground">Beverages</p>
                <p className="text-sm text-muted-foreground">45% of total orders</p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-foreground">Growth Rate</h4>
                <p className="text-green-600 font-medium">+12.5%</p>
                <p className="text-sm text-muted-foreground">Compared to previous period</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Reports;
