import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  FileText,
  Search,
  Calendar,
  DollarSign,
  User,
  Clock,
  Eye,
  Download,
  Printer,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
} from "lucide-react";

interface CheckItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface Check {
  id: string;
  checkNumber: string;
  tableNumber: string;
  server: string;
  customer?: string;
  items: CheckItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  status: "open" | "closed" | "paid" | "void";
  createdAt: string;
  updatedAt: string;
  paymentMethod?: string;
}

const ViewChecks: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedCheck, setSelectedCheck] = useState<Check | null>(null);

  const mockChecks: Check[] = [
    {
      id: "1",
      checkNumber: "CHK-001",
      tableNumber: "T-05",
      server: "John Smith",
      customer: "Alice Johnson",
      items: [
        { id: "1", name: "Cappuccino", quantity: 2, price: 3.50, total: 7.00 },
        { id: "2", name: "Caesar Salad", quantity: 1, price: 12.99, total: 12.99 },
        { id: "3", name: "Grilled Chicken", quantity: 1, price: 18.50, total: 18.50 },
      ],
      subtotal: 38.49,
      tax: 3.85,
      discount: 1.92,
      total: 40.42,
      status: "paid",
      createdAt: "2024-01-20 14:30",
      updatedAt: "2024-01-20 15:45",
      paymentMethod: "Credit Card",
    },
    {
      id: "2",
      checkNumber: "CHK-002",
      tableNumber: "T-12",
      server: "Sarah Wilson",
      items: [
        { id: "4", name: "Espresso", quantity: 1, price: 2.50, total: 2.50 },
        { id: "5", name: "Chocolate Cake", quantity: 1, price: 6.99, total: 6.99 },
      ],
      subtotal: 9.49,
      tax: 0.95,
      discount: 0.00,
      total: 10.44,
      status: "open",
      createdAt: "2024-01-20 16:15",
      updatedAt: "2024-01-20 16:15",
    },
    {
      id: "3",
      checkNumber: "CHK-003",
      tableNumber: "T-08",
      server: "Mike Davis",
      customer: "Bob Brown",
      items: [
        { id: "6", name: "Latte", quantity: 3, price: 4.25, total: 12.75 },
        { id: "7", name: "Sandwich", quantity: 2, price: 8.99, total: 17.98 },
      ],
      subtotal: 30.73,
      tax: 3.07,
      discount: 1.54,
      total: 32.26,
      status: "closed",
      createdAt: "2024-01-20 13:20",
      updatedAt: "2024-01-20 14:10",
    },
    {
      id: "4",
      checkNumber: "CHK-004",
      tableNumber: "T-03",
      server: "Emma Taylor",
      items: [
        { id: "8", name: "Green Tea", quantity: 1, price: 2.99, total: 2.99 },
      ],
      subtotal: 2.99,
      tax: 0.30,
      discount: 0.00,
      total: 3.29,
      status: "void",
      createdAt: "2024-01-20 12:45",
      updatedAt: "2024-01-20 12:50",
    },
  ];

  const filteredChecks = mockChecks.filter((check) => {
    const matchesSearch = 
      check.checkNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      check.tableNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      check.server.toLowerCase().includes(searchTerm.toLowerCase()) ||
      check.customer?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || check.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: Check["status"]) => {
    switch (status) {
      case "open":
        return "bg-blue-500/10 text-blue-600 border-blue-200";
      case "closed":
        return "bg-orange-500/10 text-orange-600 border-orange-200";
      case "paid":
        return "bg-green-500/10 text-green-600 border-green-200";
      case "void":
        return "bg-red-500/10 text-red-600 border-red-200";
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-200";
    }
  };

  const getStatusIcon = (status: Check["status"]) => {
    switch (status) {
      case "open":
        return <Clock className="h-4 w-4" />;
      case "closed":
        return <AlertCircle className="h-4 w-4" />;
      case "paid":
        return <CheckCircle className="h-4 w-4" />;
      case "void":
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getTotalsByStatus = () => {
    return mockChecks.reduce((acc, check) => {
      acc[check.status] = (acc[check.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  };

  const statusTotals = getTotalsByStatus();

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/stations/home">
            <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
              <ArrowLeft className="h-4 w-4" />
              Back to Menu
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              View Checks
            </h1>
            <p className="text-muted-foreground">
              View and manage customer checks and orders
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[
            { label: "Open", value: statusTotals.open || 0, color: "text-blue-600", bg: "bg-blue-500/10" },
            { label: "Closed", value: statusTotals.closed || 0, color: "text-orange-600", bg: "bg-orange-500/10" },
            { label: "Paid", value: statusTotals.paid || 0, color: "text-green-600", bg: "bg-green-500/10" },
            { label: "Void", value: statusTotals.void || 0, color: "text-red-600", bg: "bg-red-500/10" },
          ].map((stat) => (
            <Card key={stat.label} className="shadow-lg border-border/50">
              <CardContent className="p-4">
                <div className={`flex items-center justify-between p-3 rounded-lg ${stat.bg}`}>
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.label} Checks</p>
                    <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                  </div>
                  <FileText className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            <Card className="shadow-lg border-border/50">
              <CardHeader className="pb-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex items-center gap-2 flex-1">
                    <Search className="h-5 w-5 text-primary" />
                    <Input
                      placeholder="Search by check number, table, server, or customer..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="border-border/50"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Filter className="h-5 w-5 text-primary" />
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-32 border-border/50">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="void">Void</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Card className="shadow-lg border-border/50">
              <CardHeader>
                <CardTitle className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Checks ({filteredChecks.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="space-y-4">
                    {filteredChecks.map((check) => (
                      <Card
                        key={check.id}
                        className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border-border/50 ${
                          selectedCheck?.id === check.id
                            ? "ring-2 ring-primary/50 bg-primary/5"
                            : "hover:border-primary/50"
                        }`}
                        onClick={() => setSelectedCheck(check)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <FileText className="h-5 w-5 text-primary" />
                              <div>
                                <span className="font-bold text-lg">{check.checkNumber}</span>
                                <span className="text-muted-foreground ml-2">({check.tableNumber})</span>
                              </div>
                            </div>
                            <Badge className={getStatusColor(check.status)}>
                              {getStatusIcon(check.status)}
                              {check.status.charAt(0).toUpperCase() + check.status.slice(1)}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">Server:</span>
                              <span className="font-medium">{check.server}</span>
                            </div>
                            {check.customer && (
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <span className="text-muted-foreground">Customer:</span>
                                <span className="font-medium">{check.customer}</span>
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">Created:</span>
                              <span>{check.createdAt}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">Total:</span>
                              <span className="font-bold text-primary">${check.total.toFixed(2)}</span>
                            </div>
                          </div>

                          <div className="text-sm text-muted-foreground">
                            {check.items.length} item(s) • {check.paymentMethod || "No payment"}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            {selectedCheck ? (
              <Card className="shadow-lg border-border/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-primary" />
                    {selectedCheck.checkNumber} Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <Badge className={getStatusColor(selectedCheck.status)}>
                        {getStatusIcon(selectedCheck.status)}
                        {selectedCheck.status.charAt(0).toUpperCase() + selectedCheck.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Table:</span>
                      <span className="font-medium">{selectedCheck.tableNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Server:</span>
                      <span className="font-medium">{selectedCheck.server}</span>
                    </div>
                    {selectedCheck.customer && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Customer:</span>
                        <span className="font-medium">{selectedCheck.customer}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Created:</span>
                      <span className="font-medium">{selectedCheck.createdAt}</span>
                    </div>
                    {selectedCheck.paymentMethod && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Payment:</span>
                        <span className="font-medium">{selectedCheck.paymentMethod}</span>
                      </div>
                    )}
                  </div>

                  <div className="pt-4 border-t border-border/50">
                    <h4 className="font-semibold text-foreground mb-3">Order Items</h4>
                    <ScrollArea className="h-48">
                      <div className="space-y-2">
                        {selectedCheck.items.map((item) => (
                          <div key={item.id} className="flex justify-between items-center p-2 bg-muted/30 rounded-md">
                            <div>
                              <span className="font-medium">{item.name}</span>
                              <span className="text-muted-foreground text-sm ml-2">x{item.quantity}</span>
                            </div>
                            <span className="font-medium">${item.total.toFixed(2)}</span>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>

                  <div className="pt-4 border-t border-border/50 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Subtotal:</span>
                      <span>${selectedCheck.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Tax:</span>
                      <span>${selectedCheck.tax.toFixed(2)}</span>
                    </div>
                    {selectedCheck.discount > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Discount:</span>
                        <span>-${selectedCheck.discount.toFixed(2)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-lg font-bold pt-2 border-t border-border/50">
                      <span>Total:</span>
                      <span className="text-primary">${selectedCheck.total.toFixed(2)}</span>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button size="sm" variant="outline" className="flex-1 hover:scale-105 transition-all duration-200">
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1 hover:scale-105 transition-all duration-200">
                      <Download className="h-3 w-3 mr-1" />
                      Export
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1 hover:scale-105 transition-all duration-200">
                      <Printer className="h-3 w-3 mr-1" />
                      Print
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="shadow-lg border-border/50">
                <CardContent className="py-12">
                  <div className="text-center">
                    <FileText className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      Select a Check
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      Click on a check from the list to view detailed information
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewChecks;
