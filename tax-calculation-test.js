// Test file to demonstrate backward tax calculation
// This shows how the new tax calculation works

// Original calculation (forward):
// If subtotal = 300, then:
// tax = 300 * 0.16 = 48
// total = 300 + 48 = 348

// New calculation (backward):
// If total = 300 (including tax), then:
// subtotal = 300 / 1.16 = 258.62
// tax = 300 - 258.62 = 41.38

function calculateTaxBackward(totalWithTax) {
  // If total = subtotal * 1.16, then subtotal = total / 1.16
  const subtotal = totalWithTax / 1.16;
  const taxAmount = totalWithTax - subtotal;
  return { subtotal, taxAmount };
}

// Test cases
console.log("=== Backward Tax Calculation Test ===");

// Test 1: Total of 300
const test1 = calculateTaxBackward(300);
console.log("Total: 300");
console.log("Subtotal:", test1.subtotal.toFixed(2));
console.log("Tax:", test1.taxAmount.toFixed(2));
console.log("Verification (subtotal * 1.16):", (test1.subtotal * 1.16).toFixed(2));
console.log("");

// Test 2: Total of 116 (should give subtotal of 100)
const test2 = calculateTaxBackward(116);
console.log("Total: 116");
console.log("Subtotal:", test2.subtotal.toFixed(2));
console.log("Tax:", test2.taxAmount.toFixed(2));
console.log("Verification (subtotal * 1.16):", (test2.subtotal * 1.16).toFixed(2));
console.log("");

// Test 3: Total of 1000
const test3 = calculateTaxBackward(1000);
console.log("Total: 1000");
console.log("Subtotal:", test3.subtotal.toFixed(2));
console.log("Tax:", test3.taxAmount.toFixed(2));
console.log("Verification (subtotal * 1.16):", (test3.subtotal * 1.16).toFixed(2));

// Discount codes available:
console.log("\n=== Available Discount Codes ===");
console.log("SAVE10 - 10% off");
console.log("SAVE15 - 15% off");
console.log("SAVE20 - 20% off");
console.log("WELCOME5 - 5% off");
console.log("STUDENT - 12% off");
